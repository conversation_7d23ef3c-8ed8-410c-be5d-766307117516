using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;
using System.Globalization;

namespace BTMHubHelper.Tests;

public class AmountReaderTests
{
    private static Dictionary<string, string> ParseQueryString(string queryString)
    {
        return queryString.Split('&')
            .Select(part => part.Split('='))
            .ToDictionary(split => split[0], split => split[1]);
    }

    private static HttpRequest BuildRequest(Dictionary<string, string> query)
    {
        var context = new DefaultHttpContext();
        var dict = new Dictionary<string, StringValues>(StringComparer.OrdinalIgnoreCase);
        foreach (var kv in query)
        {
            dict[kv.Key] = new StringValues(Uri.UnescapeDataString(kv.Value));
        }
        context.Request.Query = new QueryCollection(dict);
        return context.Request;
    }

    private static IActionResult Invoke(Dictionary<string, string> query)
    {
        var func = new AmountReader(null!);
        var req = BuildRequest(query);
        return func.Run(req);
    }

    [Theory]
    [InlineData("amount=abc&currency=USD&language=EN-US&format=text", "Invalid amount format. Use . or , as decimal separator (e.g., 1234.56 or 1234,56).")]
    [InlineData("amount=1234&currency=XYZ&language=EN-US&format=text", "Invalid currency or language. Supported currencies: USD, VND. Supported languages: EN-US, EN-GB, VI.")]
    [InlineData("amount=1234&currency=USD&language=FR&format=text", "Invalid currency or language. Supported currencies: USD, VND. Supported languages: EN-US, EN-GB, VI.")]
    [InlineData("amount=1234&currency=USD&language=EN-US&format=pdf", "Invalid format. Supported: text, image.")]
    public void Run_Error_Cases(string queryString, string expectedError)
    {
        var query = ParseQueryString(queryString);
        var result = Invoke(query) as BadRequestObjectResult;
        Assert.NotNull(result);
        Assert.Equal(expectedError, result.Value);
    }

    [Fact]
    public void TryParseAmount_Valid()
    {
        Assert.True(AmountReader.TryParseAmount("1234.56", out long integer, out long fractional));
        Assert.Equal(1234, integer);
        Assert.Equal(56, fractional);
    }

    [Fact]
    public void TryParseAmount_Invalid()
    {
        Assert.False(AmountReader.TryParseAmount("abc", out _, out _));
    }

    [Fact]
    public void ApplyCapitalization_SentenceCase()
    {
        var result = AmountReader.ApplyCapitalization("test string", 1, CultureInfo.InvariantCulture);
        Assert.Equal("Test string", result);
    }

    [Fact]
    public void ApplyCapitalization_UpperCase()
    {
        var result = AmountReader.ApplyCapitalization("test string", 3, CultureInfo.InvariantCulture);
        Assert.Equal("TEST STRING", result);
    }

    // --- BỔ SUNG: Các bài kiểm thử mới từ README.md ---

    /// <summary>
    /// Phương thức này kiểm thử tất cả các trường hợp trả về kết quả dạng văn bản từ tệp README.md.
    /// Nó nhận toàn bộ chuỗi truy vấn để tăng tính linh hoạt.
    /// </summary>
    [Theory]
    // Default Cases
    [InlineData("amount=1234&currency=USD&language=EN-US", "One thousand two hundred thirty four dollars only")]
    [InlineData("amount=1234.56&currency=USD&language=EN-US&decTrans=1", "One thousand two hundred thirty four dollars and fifty six cents")]
    [InlineData("amount=1&currency=USD&language=EN-US", "One dollar only")]
    [InlineData("amount=0&currency=USD&language=EN-US", "Zero dollars only")]
    [InlineData("amount=-1234&currency=USD&language=EN-US", "Negative one thousand two hundred thirty four dollars only")]
    [InlineData("amount=1234&currency=VND&language=EN-US", "One thousand two hundred thirty four dong only")]
    [InlineData("amount=1234.56&currency=VND&language=EN-US&decTrans=1", "One thousand two hundred thirty four dong and fifty six xu")]
    [InlineData("amount=1&currency=VND&language=EN-US", "One dong only")]
    [InlineData("amount=0&currency=VND&language=EN-US", "Zero dong only")]
    [InlineData("amount=-1234&currency=VND&language=EN-US", "Negative one thousand two hundred thirty four dong only")]
    [InlineData("amount=1234&currency=USD&language=EN-GB", "One thousand two hundred and thirty four dollars only")]
    [InlineData("amount=1234.56&currency=USD&language=EN-GB&decTrans=1", "One thousand two hundred and thirty four dollars and fifty six cents")]
    [InlineData("amount=1&currency=USD&language=EN-GB", "One dollar only")]
    [InlineData("amount=0&currency=USD&language=EN-GB", "Zero dollars only")]
    [InlineData("amount=-1234&currency=USD&language=EN-GB", "Negative one thousand two hundred and thirty four dollars only")]
    [InlineData("amount=1234&currency=VND&language=VI", "Một ngàn hai trăm ba mươi bốn đồng chẵn")]
    [InlineData("amount=1234,56&currency=VND&language=VI&decTrans=1", "Một ngàn hai trăm ba mươi bốn đồng và năm mươi sáu xu")]
    [InlineData("amount=1&currency=VND&language=VI", "Một đồng chẵn")]
    [InlineData("amount=0&currency=VND&language=VI", "Không đồng")]
    [InlineData("amount=0,50&currency=VND&language=VI&decTrans=1", "Không đồng và năm xu")]
    [InlineData("amount=-1234&currency=VND&language=VI", "Âm một ngàn hai trăm ba mươi bốn đồng chẵn")]
    // Override Config
    [InlineData("amount=5000&currency=VND&language=VI&only=chỉ", "Năm ngàn đồng chỉ")]
    [InlineData("amount=5000,25&currency=VND&language=VI&decPoint=với&decTrans=1", "Năm ngàn đồng với hai mươi lăm xu")]
    [InlineData("amount=1500&currency=VND&language=VI&unit1=VNĐ", "Một ngàn năm trăm vnđ chẵn")]
    [InlineData("amount=1500,50&currency=VND&language=VI&unit1=VNĐ&unit2=xu&decTrans=1", "Một ngàn năm trăm vnđ và năm xu")]
    [InlineData("amount=1000&currency=VND&language=VI&ngan=nghìn", "Một nghìn đồng chẵn")]
    [InlineData("amount=1005&currency=VND&language=VI&le=lẻ%20nhỏ", "Một ngàn không trăm lẻ nhỏ năm đồng chẵn")]
    [InlineData("amount=4&currency=VND&language=VI&bon=tư", "Bốn đồng chẵn")]
    [InlineData("amount=7&currency=VND&language=VI&bay=bẩy", "Bẩy đồng chẵn")]
    [InlineData("amount=200&currency=USD&language=EN-US&only=only%20mate&hundredWord=hundred", "Two hundred dollars only mate")]
    [InlineData("amount=-200&currency=USD&language=EN-US&prefixForNegative=minus", "Minus two hundred dollars only")]
    [InlineData("amount=1234.50&currency=USD&language=EN-GB&decTrans=1", "One thousand two hundred and thirty four dollars and fifty cents")]
    [InlineData("amount=1234.50&currency=USD&language=EN-US&decPoint=plus&decTrans=1", "One thousand two hundred thirty four dollars plus fifty cents")]
    [InlineData("amount=999999&currency=VND&language=VI&unit1=đ", "Chín trăm chín mươi chín ngàn chín trăm chín mươi chín đ chẵn")]
    // Capitalization Styles
    [InlineData("amount=1234&currency=USD&language=EN-US&capitalizationStyles=1", "One thousand two hundred thirty four dollars only")]
    [InlineData("amount=1234&currency=USD&language=EN-US&capitalizationStyles=2", "one thousand two hundred thirty four dollars only")]
    [InlineData("amount=1234&currency=USD&language=EN-US&capitalizationStyles=3", "ONE THOUSAND TWO HUNDRED THIRTY FOUR DOLLARS ONLY")]
    [InlineData("amount=1234&currency=USD&language=EN-US&capitalizationStyles=4", "One Thousand Two Hundred Thirty Four Dollars Only")]
    // decTrans Variations
    [InlineData("amount=1234.50&currency=USD&language=EN-US&decTrans=1", "One thousand two hundred thirty four dollars and fifty cents")]
    [InlineData("amount=1234.50&currency=USD&language=EN-US&decTrans=0", "One thousand two hundred thirty four dollars")]
    [InlineData("amount=1234.05&currency=USD&language=EN-US&decTrans=1", "One thousand two hundred thirty four dollars and five cents")]
    [InlineData("amount=1234.05&currency=USD&language=EN-US&decTrans=0", "One thousand two hundred thirty four dollars")]
    [InlineData("amount=1234.50&currency=VND&language=VI&decTrans=1", "Một ngàn hai trăm ba mươi bốn đồng và năm xu")]
    [InlineData("amount=1234.50&currency=VND&language=VI&decTrans=0", "Một ngàn hai trăm ba mươi bốn đồng")]
    // Special Numbers
    [InlineData("amount=0.50&currency=USD&language=EN-US&decTrans=1", "Fifty cents")]
    [InlineData("amount=1000000&currency=USD&language=EN-US", "One million dollars only")]
    [InlineData("amount=1000000&currency=VND&language=VI", "Một triệu đồng chẵn")]
    [InlineData("amount=1000000000&currency=USD&language=EN-US", "One billion dollars only")]
    [InlineData("amount=1000000000&currency=VND&language=VI", "Một tỷ đồng chẵn")]
    [InlineData("amount=15&currency=USD&language=EN-US", "Fifteen dollars only")]
    [InlineData("amount=15&currency=VND&language=VI", "Mười lăm đồng chẵn")]
    [InlineData("amount=105&currency=VND&language=VI", "Một trăm lẻ năm đồng chẵn")]
    [InlineData("amount=1105&currency=VND&language=VI", "Một ngàn một trăm lẻ năm đồng chẵn")]
    [InlineData("amount=1000005&currency=VND&language=VI", "Một triệu không trăm lẻ năm đồng chẵn")]
    [InlineData("amount=2500000000&currency=USD&language=EN-US", "Two billion five hundred million dollars only")]
    [InlineData("amount=2500000000&currency=VND&language=VI", "Hai tỷ năm trăm triệu đồng chẵn")]
    // Special spell
    [InlineData("amount=25&currency=VND&language=VI", "Hai mươi lăm đồng chẵn")]
    [InlineData("amount=21&currency=VND&language=VI", "Hai mươi mốt đồng chẵn")]
    [InlineData("amount=35&currency=VND&language=VI", "Ba mươi lăm đồng chẵn")]
    [InlineData("amount=91&currency=VND&language=VI", "Chín mươi mốt đồng chẵn")]
    // Edge Cases - Very Small Amounts
    [InlineData("amount=0.01&currency=USD&language=EN-US&decTrans=1", "One cent")]
    [InlineData("amount=0.01&currency=USD&language=EN-GB&decTrans=1", "One cent")]
    [InlineData("amount=0,01&currency=VND&language=VI&decTrans=1", "Không đồng và không một xu")]
    [InlineData("amount=0.05&currency=USD&language=EN-US&decTrans=1", "Five cents")]
    [InlineData("amount=0.05&currency=USD&language=EN-GB&decTrans=1", "Five cents")]
    [InlineData("amount=0,05&currency=VND&language=VI&decTrans=1", "Không đồng và không năm xu")]
    [InlineData("amount=0.99&currency=USD&language=EN-US&decTrans=1", "Ninety nine cents")]
    [InlineData("amount=0.99&currency=USD&language=EN-GB&decTrans=1", "Ninety nine cents")]
    [InlineData("amount=0,99&currency=VND&language=VI&decTrans=1", "Không đồng và chín mươi chín xu")]
    // Cross-Separator Testing
    [InlineData("amount=1234,56&currency=USD&language=EN-US&decTrans=1", "One thousand two hundred thirty four dollars and fifty six cents")]
    [InlineData("amount=1234,56&currency=USD&language=EN-GB&decTrans=1", "One thousand two hundred and thirty four dollars and fifty six cents")]
    [InlineData("amount=1234.56&currency=VND&language=VI&decTrans=1", "Một ngàn hai trăm ba mươi bốn đồng và năm mươi sáu xu")]
    [InlineData("amount=5000,25&currency=USD&language=EN-GB&decTrans=1", "Five thousand dollars and twenty five cents")]
    [InlineData("amount=5000,25&currency=USD&language=EN-US&decTrans=1", "Five thousand dollars and twenty five cents")]
    [InlineData("amount=5000.25&currency=VND&language=EN-US&decTrans=1", "Five thousand dong and twenty five xu")]
    [InlineData("amount=5000.25&currency=VND&language=VI&decTrans=1", "Năm ngàn đồng và hai mươi lăm xu")]
    // Large Numbers with Decimals
    [InlineData("amount=1000000.50&currency=USD&language=EN-US&decTrans=1", "One million dollars and fifty cents")]
    [InlineData("amount=1000000,50&currency=VND&language=VI&decTrans=1", "Một triệu đồng và năm xu")]
    [InlineData("amount=1000000000.99&currency=USD&language=EN-US&decTrans=1", "One billion dollars and ninety nine cents")]
    [InlineData("amount=1000000000,99&currency=VND&language=VI&decTrans=1", "Một tỷ đồng và chín mươi chín xu")]
    // Vietnamese Number Patterns
    [InlineData("amount=11&currency=VND&language=VI", "Mười một đồng chẵn")]
    [InlineData("amount=111&currency=VND&language=VI", "Một trăm mười một đồng chẵn")]
    [InlineData("amount=1111&currency=VND&language=VI", "Một ngàn một trăm mười một đồng chẵn")]
    [InlineData("amount=11111&currency=VND&language=VI", "Mười một ngàn một trăm mười một đồng chẵn")]
    [InlineData("amount=12&currency=VND&language=VI", "Mười hai đồng chẵn")]
    [InlineData("amount=20&currency=VND&language=VI", "Hai mươi đồng chẵn")]
    [InlineData("amount=101&currency=VND&language=VI", "Một trăm lẻ một đồng chẵn")]
    [InlineData("amount=1001&currency=VND&language=VI", "Một ngàn không trăm lẻ một đồng chẵn")]
    [InlineData("amount=10001&currency=VND&language=VI", "Mười ngàn không trăm lẻ một đồng chẵn")]
    // Multiple Custom Parameters
    [InlineData("amount=1234&currency=VND&language=VI&bon=tư&bay=bẩy&ngan=nghìn", "Một nghìn hai trăm ba mươi tư đồng chẵn")]
    [InlineData("amount=4567&currency=VND&language=VI&bon=tư&bay=bẩy&only=hoàn toàn", "Bốn ngàn năm trăm sáu mươi bẩy đồng hoàn toàn")]
    // Test "tư" only applies to tens (24, 34, 54, 64, 74, 84, 94)
    [InlineData("amount=24&currency=VND&language=VI&bon=tư", "Hai mươi tư đồng chẵn")]
    [InlineData("amount=34&currency=VND&language=VI&bon=tư", "Ba mươi tư đồng chẵn")]
    [InlineData("amount=14&currency=VND&language=VI&bon=tư", "Mười bốn đồng chẵn")]
    [InlineData("amount=200&currency=USD&language=EN-US&only=exactly&prefixForNegative=minus&hundredWord=hundred", "Two hundred dollars exactly")]
    [InlineData("amount=-500&currency=USD&language=EN-US&prefixForNegative=minus&only=precisely", "Minus five hundred dollars precisely")]
    // EN-GB Extended Coverage
    [InlineData("amount=1234&currency=VND&language=EN-GB", "One thousand two hundred and thirty four dong only")]
    [InlineData("amount=1234.56&currency=VND&language=EN-GB&decTrans=1", "One thousand two hundred and thirty four dong and fifty six xu")]
    [InlineData("amount=0&currency=VND&language=EN-GB", "Zero dong only")]
    [InlineData("amount=-1234&currency=VND&language=EN-GB", "Negative one thousand two hundred and thirty four dong only")]
    [InlineData("amount=1000000&currency=USD&language=EN-GB", "One million dollars only")]
    [InlineData("amount=1000000&currency=VND&language=EN-GB", "One million dong only")]
    // Boundary Values
    [InlineData("amount=999999999999&currency=USD&language=EN-US", "Nine hundred ninety nine billion nine hundred ninety nine million nine hundred ninety nine thousand nine hundred ninety nine dollars only")]
    [InlineData("amount=999999999999&currency=USD&language=EN-GB", "Nine hundred and ninety nine billion nine hundred and ninety nine million nine hundred and ninety nine thousand nine hundred and ninety nine dollars only")]
    [InlineData("amount=1&currency=USD&language=EN-US&decTrans=1", "One dollar only")]
    [InlineData("amount=1.00&currency=USD&language=EN-US&decTrans=1", "One dollar only")]
    [InlineData("amount=0.00&currency=USD&language=EN-US&decTrans=1", "Zero dollars only")]
    // Special Decimal Patterns
    [InlineData("amount=1234.10&currency=USD&language=EN-US&decTrans=1", "One thousand two hundred thirty four dollars and ten cents")]
    [InlineData("amount=1234.10&currency=USD&language=EN-GB&decTrans=1", "One thousand two hundred and thirty four dollars and ten cents")]
    [InlineData("amount=1234,10&currency=VND&language=VI&decTrans=1", "Một ngàn hai trăm ba mươi bốn đồng và một xu")]
    [InlineData("amount=1234.01&currency=USD&language=EN-US&decTrans=1", "One thousand two hundred thirty four dollars and one cent")]
    [InlineData("amount=1234.01&currency=USD&language=EN-GB&decTrans=1", "One thousand two hundred and thirty four dollars and one cent")]
    [InlineData("amount=1234,01&currency=VND&language=VI&decTrans=1", "Một ngàn hai trăm ba mươi bốn đồng và không một xu")]
    public void Run_Readme_Text_Cases(string queryString, string expected)
    {
        var query = ParseQueryString(queryString);
        var result = Invoke(query) as OkObjectResult;
        Assert.NotNull(result);
        Assert.Equal(expected, result.Value);
    }

    /// <summary>
    /// Phương thức này kiểm thử các trường hợp trả về kết quả dạng hình ảnh từ tệp README.md.
    /// Nó kiểm tra loại kết quả và ContentType thay vì nội dung.
    /// </summary>
    [Theory]
    [InlineData("amount=1234&currency=USD&language=EN-US&format=image", "image/png")]
    [InlineData("amount=1234&currency=USD&language=EN-US&format=image&imageType=jpg", "image/jpg")]
    [InlineData("amount=1234&currency=USD&language=EN-US&format=image&fontName=Arial", "image/png")]
    [InlineData("amount=1234&currency=USD&language=EN-US&format=image&imageType=jpg&fontSize=20", "image/jpg")]
    [InlineData("amount=1234&currency=USD&language=EN-US&format=image&color=Red", "image/png")]
    [InlineData("amount=1234&currency=VND&language=VI&format=image&imageType=jpg", "image/jpg")]
    [InlineData("amount=1234&currency=VND&language=VI&format=image&imageType=jpg&fontName=Times New Roman", "image/jpg")]
    [InlineData("amount=1234&currency=VND&language=VI&format=image&fontSize=18", "image/png")]
    [InlineData("amount=1234&currency=VND&language=VI&format=image&imageType=jpg&color=Blue", "image/jpg")]
    [InlineData("amount=0,50&currency=VND&language=VI&format=image&decTrans=1", "image/png")]
    // Giả sử có hỗ trợ imageType khác png
    // [InlineData("amount=1234&currency=USD&language=EN-US&format=image&imageType=jpeg", "image/jpeg")]
    public void Run_Readme_Image_Cases(string queryString, string expectedContentType)
    {
        var query = ParseQueryString(queryString);
        var result = Invoke(query);
        var fileResult = Assert.IsType<FileContentResult>(result);
        Assert.Equal(expectedContentType, fileResult.ContentType);
    }
}