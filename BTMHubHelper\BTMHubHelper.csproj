﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AzureFunctionsVersion>v4</AzureFunctionsVersion>
    <OutputType>Exe</OutputType>
    <ImplicitUsings>disable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <PropertyGroup>
    <!-- Drawing or SkiaSharp-->
    <ImageRendering>SkiaSharp</ImageRendering>
  </PropertyGroup>

  <Choose>
    <When Condition="'$(ImageRendering)'=='Drawing'">
      <PropertyGroup>
        <DefineConstants>$(DefineConstants);USE_DRAWING;</DefineConstants>
      </PropertyGroup>
      <ItemGroup>
        <PackageReference Include="System.Drawing.Common" Version="9.0.8" />
      </ItemGroup>
    </When>
    <When Condition="'$(ImageRendering)'=='SkiaSharp'">
      <PropertyGroup>
        <DefineConstants>$(DefineConstants);USE_SKIASHARP;</DefineConstants>
      </PropertyGroup>
      <ItemGroup>
        <PackageReference Include="SkiaSharp" Version="3.119.0" />
      </ItemGroup>
    </When>
  </Choose>

  <ItemGroup>
    <None Remove="AmountReader.cs.bak" />
  </ItemGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="Humanizer.Core" Version="2.14.1" />
    <!-- Application Insights isn't enabled by default. See https://aka.ms/AAt8mw4. -->
    <!-- <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.22.0" /> -->
    <!-- <PackageReference Include="Microsoft.Azure.Functions.Worker.ApplicationInsights" Version="2.0.0" /> -->
    <PackageReference Include="Microsoft.Azure.Functions.Worker" Version="2.0.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http" Version="3.2.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore" Version="2.0.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Sdk" Version="2.0.0" />
  </ItemGroup>
  <ItemGroup>
    <None Update="host.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="local.settings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>Never</CopyToPublishDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Using Include="System.Threading.ExecutionContext" Alias="ExecutionContext" />
  </ItemGroup>
</Project>