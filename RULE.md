# 📋 PROJECT RULES & GUIDELINES

## 🎯 **PROJECT OVERVIEW**
- **Project**: BTM Amount Reader - Vietnamese/English number-to-words conversion
- **Type**: Azure Function (C# .NET)
- **Architecture**: Synchronous processing
- **Languages**: Vietnamese (VI), English (EN-US, EN-GB)
- **Currencies**: USD, VND

---

## 🔧 **DEVELOPMENT RULES**

### **1. CODE STYLE & ORGANIZATION**
- ✅ **Clean Code**: Remove meaningless comments, extra blank lines
- ✅ **No Hard-coding**: Use configuration objects instead of magic strings
- ✅ **Pure Math**: Use `Math.Log10()` and `Math.Pow()` for dynamic calculations
- ✅ **Consistent Naming**: Use descriptive method and variable names
- ❌ **No Async**: Keep all operations synchronous (Azure Function requirement)

### **2. CONFIGURATION MANAGEMENT**
- ✅ **Dynamic Configuration**: Use `LanguageConfig` objects
- ✅ **Parameter Override**: Support query parameter customization
- ✅ **Fallback Values**: Always provide default values
- ❌ **No Hard-coded Values**: Avoid "tỷ", "triệu", "billion" in code

### **3. NUMBER CONVERSION LOGIC**
- ✅ **Math-driven Approach**: Use `(int)(Math.Log10(number) / 3)` for scale detection
- ✅ **Unified Handling**: Same pattern for Vietnamese and English
- ✅ **Special Cases**: Handle Vietnamese remainder formatting ("không trăm lẻ")
- ✅ **Custom Parameters**: Support "ngan", "bon", "bay" overrides

---

## 🧪 **TESTING REQUIREMENTS**

### **1. UNIT TEST STANDARDS**
- ✅ **100% Pass Rate**: All 133 test cases must pass
- ✅ **Edge Cases**: Test zero amounts, negative numbers, fractional-only
- ✅ **Parameter Validation**: Test invalid inputs and error messages
- ✅ **Format Support**: Test TEXT and IMAGE formats

### **2. TEST-DRIVEN DEVELOPMENT**
- ✅ **Run Tests First**: Always run `dotnet test` before making changes
- ✅ **Incremental Changes**: Make small changes and test frequently
- ✅ **Regression Testing**: Ensure existing functionality remains intact

---

## 🏗️ **ARCHITECTURE PRINCIPLES**

### **1. SEPARATION OF CONCERNS**
- ✅ **Single Responsibility**: Each method should do one thing well
- ✅ **Pure Functions**: Prefer static methods with no side effects
- ✅ **Helper Methods**: Extract reusable logic into separate methods

### **2. EXTENSIBILITY**
- ✅ **Scalable Design**: Easy to add new languages/currencies
- ✅ **Configuration-driven**: Behavior controlled by config objects
- ✅ **Math-based Logic**: Algorithms that work for any scale

---

## 📝 **CODE QUALITY STANDARDS**

### **1. CLEAN CODE PRACTICES**
```csharp
// ✅ GOOD: Descriptive and clean
var power = (int)(Math.Log10(number) / 3);
var word = config.Thousands[power];

// ❌ BAD: Hard-coded and unclear  
if (number >= 1000000000) { result += "tỷ"; }
```

### **2. CONFIGURATION USAGE**
```csharp
// ✅ GOOD: Use configuration
var result = $"{config.ZeroWord} {config.HundredWord}";

// ❌ BAD: Hard-coded strings
var result = "không trăm";
```

### **3. ERROR HANDLING**
```csharp
// ✅ GOOD: Proper validation
if (!TryParseAmount(amountStr, out long integer, out long fractional))
{
    return new BadRequestObjectResult("Invalid amount format...");
}

// ❌ BAD: No validation
var amount = long.Parse(amountStr); // Can throw exception
```

---

## 🚀 **REFACTORING GUIDELINES**

### **1. ALLOWED REFACTORING**
- ✅ **Extract Methods**: Break down large methods
- ✅ **Remove Duplicates**: Eliminate repeated code
- ✅ **Improve Naming**: Use more descriptive names
- ✅ **Add Helper Methods**: Create reusable utilities

### **2. FORBIDDEN CHANGES**
- ❌ **No Async**: Don't convert to async/await
- ❌ **No Breaking Changes**: Maintain API compatibility
- ❌ **No External Dependencies**: Keep minimal dependencies
- ❌ **No Complex Patterns**: Avoid over-engineering

---

## 📊 **PERFORMANCE REQUIREMENTS**

### **1. EFFICIENCY**
- ✅ **O(log n) Complexity**: Use math-based algorithms
- ✅ **Minimal Allocations**: Reuse objects where possible
- ✅ **Fast Execution**: Synchronous operations only

### **2. MEMORY USAGE**
- ✅ **Static Methods**: Prefer static over instance methods
- ✅ **String Efficiency**: Use StringBuilder for complex concatenations
- ✅ **No Caching**: Keep stateless for Azure Functions

---

## 🔍 **CODE REVIEW CHECKLIST**

### **Before Committing:**
- [ ] All 133 unit tests pass
- [ ] No hard-coded strings or magic numbers
- [ ] Clean code with minimal comments
- [ ] Math-driven approach used
- [ ] Configuration objects utilized
- [ ] No async/await patterns
- [ ] Proper error handling
- [ ] Descriptive method names

### **Before Deployment:**
- [ ] Full test suite passes
- [ ] No breaking changes to API
- [ ] Performance benchmarks met
- [ ] Code review completed
- [ ] Documentation updated

---

## 📚 **DOCUMENTATION STANDARDS**

### **1. CODE DOCUMENTATION**
- ✅ **XML Comments**: For public methods only
- ✅ **Minimal Comments**: Code should be self-documenting
- ❌ **No Obvious Comments**: Avoid stating the obvious

### **2. COMMIT MESSAGES**
```
✅ GOOD: "Implement math-driven approach for number conversion"
❌ BAD: "Fix bug"
```

---

## 🎯 **SUCCESS METRICS**

### **1. QUALITY METRICS**
- **Test Coverage**: 100% pass rate (133/133 tests)
- **Code Quality**: No hard-coded values, clean structure
- **Performance**: Fast synchronous execution
- **Maintainability**: Easy to extend and modify

### **2. FUNCTIONAL METRICS**
- **Accuracy**: Correct Vietnamese/English conversion
- **Flexibility**: Support for parameter customization
- **Reliability**: Consistent behavior across all inputs
- **Scalability**: Easy to add new languages/currencies

---

*Last Updated: 2025-01-16*
*Version: 1.0*
