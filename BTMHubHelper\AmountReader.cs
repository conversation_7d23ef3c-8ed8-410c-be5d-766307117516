using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
#if USE_SKIASHARP
using SkiaSharp;
#endif
using System;
#if USE_DRAWING
using System.Drawing;
using System.Drawing.Imaging;
#endif
using System.Collections.Generic;
using System.Globalization;
using System.IO;

namespace BTMHubHelper;

public class AmountReader
{
    private readonly ILogger<AmountReader> _logger;

    public AmountReader(ILogger<AmountReader> logger)
    {
        _logger = logger;
    }

    private class LanguageConfig
    {
        public string DecPoint { get; set; } = string.Empty;
        public string OnlyText { get; set; } = string.Empty;
        public string Unit1 { get; set; } = string.Empty;
        public string Unit2 { get; set; } = string.Empty;
        public string HundredWord { get; set; } = string.Empty;
        public string ZeroWord { get; set; } = string.Empty;
        public string Conjunction { get; set; } = string.Empty;
        public string TensSeparator { get; set; } = string.Empty;
        public bool DecTrans { get; set; } = false;
        public string[] Thousands { get; set; } = [];
        public string ThousandWord { get; set; } = string.Empty;
        public string OneWord { get; set; } = string.Empty;
        public string FourWord { get; set; } = string.Empty;
        public string FiveWord { get; set; } = string.Empty;
        public string SevenWord { get; set; } = string.Empty;
        public string PrefixForNegative { get; set; } = string.Empty;
        public string[] Ones { get; set; } = [];
        public string[] Tens { get; set; } = [];
    }

    private static readonly Dictionary<string, Dictionary<string, (string Unit1, string Unit2)>> CurrencyConfig = new()
    {
        {
            "EN-US", new Dictionary<string, (string Unit1, string Unit2)>
            {
                { "USD", ("dollars", "cents") },
                { "VND", ("dong", "xu") },
            }
        },
        {
            "EN-GB", new Dictionary<string, (string Unit1, string Unit2)>
            {
                { "USD", ("dollars", "cents") },
                { "VND", ("dong", "xu") },
            }
        },
        {
            "VI", new Dictionary<string, (string Unit1, string Unit2)>
            {
                { "USD", ("đô la Mỹ", "cent Mỹ") },
                { "VND", ("đồng", "xu") },
            }
        },
    };

    private static readonly Dictionary<string, LanguageConfig> LanguageDefaults = new()
    {
        {
            "EN-US", new LanguageConfig
            {
                DecPoint = "and",
                OnlyText = "only",
                HundredWord = "hundred",
                ZeroWord = "zero",
                Conjunction = "",
                TensSeparator = " ",
                DecTrans = true,
                Thousands = ["", "thousand", "million", "billion"],
                ThousandWord = "",
                OneWord = "",
                FourWord = "",
                FiveWord = "",
                SevenWord = "",
                PrefixForNegative = "negative",
                Ones = ["", "one", "two", "three", "four", "five", "six", "seven", "eight", "nine", "ten", "eleven", "twelve", "thirteen", "fourteen", "fifteen", "sixteen", "seventeen", "eighteen", "nineteen"],
                Tens = ["", "", "twenty", "thirty", "forty", "fifty", "sixty", "seventy", "eighty", "ninety"]
            }
        },
        {
            "EN-GB", new LanguageConfig
            {
                DecPoint = "and",
                OnlyText = "only",
                HundredWord = "hundred",
                ZeroWord = "zero",
                Conjunction = "",
                TensSeparator = " ",
                DecTrans = true,
                Thousands = ["", "thousand", "million", "billion"],
                ThousandWord = "",
                OneWord = "",
                FourWord = "",
                FiveWord = "",
                SevenWord = "",
                PrefixForNegative = "negative",
                Ones = ["", "one", "two", "three", "four", "five", "six", "seven", "eight", "nine", "ten", "eleven", "twelve", "thirteen", "fourteen", "fifteen", "sixteen", "seventeen", "eighteen", "nineteen"],
                Tens = ["", "", "twenty", "thirty", "forty", "fifty", "sixty", "seventy", "eighty", "ninety"]
            }
        },
        {
            "VI", new LanguageConfig
            {
                DecPoint = "và",
                OnlyText = "chẵn",
                HundredWord = "trăm",
                ZeroWord = "không",
                Conjunction = "lẻ",
                TensSeparator = " ",
                DecTrans = true,
                Thousands = ["", "ngàn", "triệu", "tỷ"],
                ThousandWord = "ngàn",
                OneWord = "mốt",
                FourWord = "",
                FiveWord = "lăm",
                SevenWord = "",
                PrefixForNegative = "âm",
                Ones = ["", "một", "hai", "ba", "bốn", "năm", "sáu", "bảy", "tám", "chín", "mười", "mười một", "mười hai", "mười ba", "mười bốn", "mười lăm", "mười sáu", "mười bảy", "mười tám", "mười chín"],
                Tens = ["", "", "hai mươi", "ba mươi", "bốn mươi", "năm mươi", "sáu mươi", "bảy mươi", "tám mươi", "chín mươi"]
            }
        }
    };

    private static string GetQueryParameter(HttpRequest req, string key, string defaultValue = "")
    {
        return req.Query.TryGetValue(key, out StringValues value) && !string.IsNullOrEmpty(value) ? value.ToString() : defaultValue;
    }

    private static LanguageConfig GetLanguageConfig(string language, string currency, HttpRequest req)
    {
        if (!LanguageDefaults.TryGetValue(language, out var config))
        {
            throw new ArgumentException("Invalid language.");
        }

        if (!CurrencyConfig.TryGetValue(language, out var value) || !value.TryGetValue(currency, out var currencyUnits))
        {
            throw new ArgumentException("Invalid currency for the specified language.");
        }

        var result = new LanguageConfig
        {
            DecPoint = GetQueryParameter(req, "decPoint", config.DecPoint),
            OnlyText = GetQueryParameter(req, "only", config.OnlyText),
            Unit1 = GetQueryParameter(req, "unit1", currencyUnits.Unit1),
            Unit2 = GetQueryParameter(req, "unit2", currencyUnits.Unit2),
            HundredWord = GetQueryParameter(req, "hundredWord", config.HundredWord),
            ZeroWord = config.ZeroWord,
            Conjunction = GetQueryParameter(req, "le", config.Conjunction),
            TensSeparator = config.TensSeparator,
            DecTrans = GetQueryParameter(req, "decTrans", config.DecTrans ? "1" : "0") != "0",
            Thousands = config.Thousands,
            ThousandWord = GetQueryParameter(req, "ngan", config.ThousandWord),
            OneWord = config.OneWord,
            FourWord = GetQueryParameter(req, "bon", config.FourWord),
            FiveWord = config.FiveWord,
            SevenWord = GetQueryParameter(req, "bay", config.SevenWord),
            PrefixForNegative = GetQueryParameter(req, "prefixForNegative", config.PrefixForNegative),
            Ones = config.Ones,
            Tens = config.Tens
        };

        return result;
    }

    public static bool TryParseAmount(string amountStr, out long integer, out long fractional)
    {
        integer = 0;
        fractional = 0;

        if (string.IsNullOrEmpty(amountStr))
            return false;

        var parts = amountStr.Split(['.', ','], 2);

        if (!long.TryParse(parts[0], out integer))
            return false;

        if (parts.Length > 1 && !string.IsNullOrEmpty(parts[1]))
        {
            var decimalPart = parts[1];
            if (decimalPart.Length > 2) decimalPart = decimalPart[..2];
            if (!long.TryParse(decimalPart, out fractional))
                return false;
        }

        return true;
    }

    private static string NumberToWords(long number, LanguageConfig config, string language)
    {
        if (number == 0)
        {
            return config.ZeroWord;
        }

        if (language == "VI")
        {
            return ConvertVietnameseNumber(number, config);
        }
        else
        {
            return ConvertEnglishNumber(number, config, language);
        }
    }

    private static string GetWordForPower(int power, LanguageConfig config)
    {
        if (power == 1 && !string.IsNullOrEmpty(config.ThousandWord))
            return config.ThousandWord;

        return config.Thousands[power];
    }

    private static void HandleLargeNumbers(List<string> parts, ref long number, LanguageConfig config, bool addAnd)
    {
        var maxPower = config.Thousands.Length - 1;

        for (int power = maxPower; power >= 1; power--)
        {
            var divisor = (long)Math.Pow(1000, power);
            if (number >= divisor)
            {
                var word = config.Thousands[power];
                CollectEnglishParts(parts, ref number, divisor, word, config, addAnd);
            }
        }
    }

    private static string HandleVietnameseRemainder(long remainder, int power, LanguageConfig config)
    {
        if (power >= 1)
        {
            if (remainder < 10)
            {
                return $"{config.ZeroWord} {config.HundredWord} {config.Conjunction} {config.Ones[remainder]}";
            }
            else if (remainder < 100)
            {
                return $"{config.ZeroWord} {config.HundredWord} {ConvertVietnameseNumber(remainder, config)}";
            }
        }

        return ConvertVietnameseNumber(remainder, config);
    }

    private static string ConvertVietnameseNumber(long number, LanguageConfig config)
    {
        if (number == 0) return config.ZeroWord;
        if (number < 0) return $"{config.PrefixForNegative} {ConvertVietnameseNumber(-number, config)}";

        const long OneHundred = 100;

        if (number >= 1000)
        {
            var power = (int)(Math.Log10(number) / 3);
            if (power > 0 && power < config.Thousands.Length)
            {
                var divisor = (long)Math.Pow(1000, power);
                var quotient = number / divisor;
                var remainder = number % divisor;

                var word = GetWordForPower(power, config);
                var result = $"{ConvertVietnameseNumber(quotient, config)} {word}";

                if (remainder > 0)
                {
                    result += $" {HandleVietnameseRemainder(remainder, power, config)}";
                }

                return result;
            }
        }


        if (number >= OneHundred)
        {
            var hundreds = number / OneHundred;
            var remainder = number % OneHundred;
            var result = $"{config.Ones[hundreds]} {config.HundredWord}";

            if (remainder > 0)
            {
                if (remainder < 10 && remainder > 0)
                {
                    result += $" {config.Conjunction} {config.Ones[remainder]}";
                }
                else
                {
                    result += $" {ConvertVietnameseNumber(remainder, config)}";
                }
            }

            return result;
        }

        if (number >= 20)
        {
            var tens = number / 10;
            var units = number % 10;
            var result = config.Tens[tens];

            if (units > 0)
            {
                var unitWord = units switch
                {
                    // Số 5: "năm" nếu hàng chục = 0, "lăm" nếu hàng chục ≥ 1
                    5 => tens == 0 ? config.Ones[5] : (!string.IsNullOrEmpty(config.FiveWord) ? config.FiveWord : "lăm"),
                    // Số 1: "một" nếu hàng chục = 0 hoặc 1, "mốt" nếu hàng chục ≥ 2
                    1 => tens <= 1 ? config.Ones[1] : (!string.IsNullOrEmpty(config.OneWord) ? config.OneWord : "mốt"),
                    // Số 4: "bốn" nếu hàng chục = 0 hoặc 1, "tư" nếu hàng chục ≥ 2
                    4 => tens <= 1 ? config.Ones[4] : (!string.IsNullOrEmpty(config.FourWord) ? config.FourWord : "tư"),
                    7 when !string.IsNullOrEmpty(config.SevenWord) => config.SevenWord,
                    _ => config.Ones[units]
                };
                result += $" {unitWord}";
            }

            return result;
        }

        if (number >= 10 && number <= 19)
        {
            return config.Ones[number];
        }


        if (number > 0)
        {
            if (number == 7 && !string.IsNullOrEmpty(config.SevenWord))
                return config.SevenWord;

            return config.Ones[number];
        }

        return "";
    }

    private static string ConvertEnglishNumber(long number, LanguageConfig config, string language)
    {
        if (number == 0) return config.ZeroWord;
        if (number < 0) return $"{config.PrefixForNegative} {ConvertEnglishNumber(-number, config, language)}";

        var parts = new List<string>(20);
        var addAnd = language == "EN-GB";

        HandleLargeNumbers(parts, ref number, config, addAnd);

        CollectEnglishPartsUnderThousand(parts, number, config, addAnd);

        return string.Join(" ", parts);
    }

    private static void CollectEnglishParts(List<string> parts, ref long number, long divisor, string word, LanguageConfig config, bool addAnd)
    {
        var result = number / divisor;
        if (result == 0) return;

        CollectEnglishPartsUnderThousand(parts, result, config, addAnd);
        parts.Add(word);

        number %= divisor;
    }

    private static void CollectEnglishPartsUnderThousand(List<string> parts, long number, LanguageConfig config, bool addAnd)
    {
        if (number >= 100)
        {
            parts.Add(config.Ones[number / 100]);
            parts.Add(config.HundredWord);
            number %= 100;
        }

        if (number == 0) return;


        if (parts.Count > 0 && addAnd)
        {
            parts.Add(config.DecPoint);
        }

        if (number >= 20)
        {
            var tens = config.Tens[number / 10];
            var units = number % 10;

            if (units == 0)
            {
                parts.Add(tens);
            }
            else
            {

                parts.Add($"{tens}{config.TensSeparator}{config.Ones[units]}");
            }
        }
        else
        {
            parts.Add(config.Ones[number]);
        }
    }

    private string ConvertToText(long integer, long fractional, LanguageConfig config, bool decTrans, string language)
    {
        _logger?.LogInformation($"Debug - integer: {integer}, fractional: {fractional}");

        bool isNegative = integer < 0;
        long absInteger = Math.Abs(integer);
        if (absInteger == 0 && fractional > 0 && decTrans)
        {

            long actualFractional = fractional;
            if (config.Unit2 == "xu" && fractional % 10 == 0 && fractional > 0)
            {
                actualFractional = fractional / 10;
            }

            if (language == "VI")
            {

                var fractionalWords = (fractional < 10 && fractional > 0)
                    ? $"{config.ZeroWord} {NumberToWords(actualFractional, config, language)}"
                    : NumberToWords(actualFractional, config, language);

                var result = $"{config.ZeroWord} {config.Unit1} {config.DecPoint} {fractionalWords} {config.Unit2}";

                if (isNegative)
                {
                    result = config.PrefixForNegative + " " + result;
                }

                return result.Trim();
            }
            else
            {

                var fractionalWords = NumberToWords(actualFractional, config, language);
                var unit = actualFractional == 1 ? config.Unit2.Replace("s", "") : config.Unit2;
                var result = $"{fractionalWords} {unit}";

                if (isNegative)
                {
                    result = config.PrefixForNegative + " " + result;
                }

                return result.Trim();
            }
        }
        string numText = NumberToWords(absInteger, config, language);
        string text = numText;
        text += Math.Abs(integer) == 1 ? " " + config.Unit1.Replace("s", "") : " " + config.Unit1;

        if (fractional > 0 && decTrans)
        {
            text += " " + config.DecPoint + " ";


            long actualFractional = fractional;
            if (config.Unit2 == "xu" && fractional % 10 == 0 && fractional > 0)
            {
                actualFractional = fractional / 10;
            }


            if (language == "VI" && fractional < 10 && fractional > 0)
            {

                var fractionalWords = $"{config.ZeroWord} {NumberToWords(actualFractional, config, language)}";
                text += fractionalWords + " " + config.Unit2;
            }
            else if (language == "VI")
            {

                text += NumberToWords(actualFractional, config, language) + " " + config.Unit2;
            }
            else
            {
                text += NumberToWords(actualFractional, config, language) + (Math.Abs(actualFractional) == 1 ? " " + config.Unit2.Replace("s", "") : " " + config.Unit2);
            }
        }
        else if (fractional == 0)
        {

            if (!(Math.Abs(integer) == 0 && language == "VI"))
            {
                text += " " + config.OnlyText;
            }
        }

        if (isNegative)
        {
            text = config.PrefixForNegative + " " + text;
        }

        return text.Trim();
    }

    public static string ApplyCapitalization(string text, int capitalizationStyles, CultureInfo culture)
    {
        if (string.IsNullOrEmpty(text)) return text;

        return capitalizationStyles switch
        {
            1 => char.ToUpper(text[0], culture) + text[1..].ToLower(culture),
            2 => text.ToLower(culture),
            3 => text.ToUpper(culture),
            4 => culture.TextInfo.ToTitleCase(text.ToLower(culture)),
            _ => text
        };
    }

    [Function("AmountReader")]
    public IActionResult Run([HttpTrigger(AuthorizationLevel.Anonymous, "get")] HttpRequest req)
    {
        _logger?.LogInformation("C# HTTP trigger function processed a request.");

        var amountStr = GetQueryParameter(req, "amount");
        var currency = GetQueryParameter(req, "currency").ToUpperInvariant();
        var language = GetQueryParameter(req, "language").ToUpperInvariant();
        var format = GetQueryParameter(req, "format", "text").ToUpperInvariant();

        if (string.IsNullOrEmpty(amountStr) || string.IsNullOrEmpty(currency) || string.IsNullOrEmpty(language))
        {
            return new BadRequestObjectResult("Missing required parameters: amount, currency (USD/VND), language (EN-US/EN-GB/VI).");
        }

        if (!CurrencyConfig.TryGetValue(language, out var value) || !value.ContainsKey(currency))
        {
            return new BadRequestObjectResult("Invalid currency or language. Supported currencies: USD, VND. Supported languages: EN-US, EN-GB, VI.");
        }

        if (format != "TEXT" && format != "IMAGE")
        {
            return new BadRequestObjectResult("Invalid format. Supported: text, image.");
        }

        try
        {
            var config = GetLanguageConfig(language, currency, req);

            var capStyleStr = GetQueryParameter(req, "capitalizationStyles", "1");
            _ = int.TryParse(capStyleStr, out int capitalizationStyles);

            var fontName = GetQueryParameter(req, "fontName", "Times New Roman");
            var fontSizeStr = GetQueryParameter(req, "fontSize", "10");
            _ = float.TryParse(fontSizeStr, out var fontSize);
            if (fontSize < 1)
            {
                fontSize = 10f;
            }
            var colorStr = GetQueryParameter(req, "color", "Black");
            var imageType = GetQueryParameter(req, "imageType", "png").ToLowerInvariant();

            if (!TryParseAmount(amountStr, out long integer, out long fractional))
            {
                _logger?.LogError($"Failed to parse amount: {amountStr}");
                return new BadRequestObjectResult("Invalid amount format. Use . or , as decimal separator (e.g., 1234.56 or 1234,56).");
            }

            var resultText = ConvertToText(integer, fractional, config, config.DecTrans, language);
            resultText = ApplyCapitalization(resultText, capitalizationStyles, CultureInfo.InvariantCulture);

            if (format == "TEXT")
            {
                return new OkObjectResult(resultText);
            }
            else
            {
                using var ms = new MemoryStream();
#if USE_DRAWING
                int width = (int)(resultText.Length * fontSize * 0.6f) + 20;
                int height = (int)(fontSize * 2) + 10;
                using (var bitmap = new Bitmap(width, height))
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    graphics.Clear(Color.White);
                    using (var font = new Font(fontName, fontSize))
                    using (var brush = new SolidBrush(GetColorFromString(colorStr)))
                    {
                        float x = 10f;
                        float y = (height - fontSize) / 2f;
                        graphics.DrawString(resultText, font, brush, new PointF(x, y));
                    }
                    ImageFormat imgFormat = imageType == "jpg" ? ImageFormat.Jpeg : ImageFormat.Png;
                    bitmap.Save(ms, imgFormat);
                }
#endif
#if USE_SKIASHARP
                using (var skFont = new SKFont())
                {
                    skFont.Typeface = SKTypeface.FromFamilyName(fontName);
                    skFont.Size = fontSize;
                    var textBounds = new SKRect();
                    float textWidth = skFont.MeasureText(resultText, out textBounds);
                    float textHeight = textBounds.Height > 0 ? textBounds.Height : skFont.Size * 1.2f;
                    using var skPaint = new SKPaint();
                    skPaint.Color = GetSKColorFromString(colorStr);
                    skPaint.IsAntialias = true;
                    int width = (int)(textWidth + 20);
                    int height = (int)(textHeight + 10);
                    using var bitmap = new SKBitmap(width, height);
                    using var canvas = new SKCanvas(bitmap);
                    canvas.Clear(SKColors.White);
                    float x = 10f;
                    float y = (height + fontSize) / 2f;
                    canvas.DrawText(resultText, x, y, skFont, skPaint);
                    SKEncodedImageFormat imgFormat = imageType == "jpg" ? SKEncodedImageFormat.Jpeg : SKEncodedImageFormat.Png;
                    using var image = SKImage.FromBitmap(bitmap);
                    using var data = image.Encode(imgFormat, 100);
                    data.SaveTo(ms);
                }
#endif
                return new FileContentResult(ms.ToArray(), $"image/{imageType}");
            }
        }
        catch (ArgumentException ex)
        {
            _logger?.LogError(ex, "Argument error in ImprovedAmountReader");
            return new BadRequestObjectResult(ex.Message);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Unexpected error in ImprovedAmountReader");
            return new BadRequestObjectResult($"An error occurred: {ex.Message}");
        }
    }

#if USE_DRAWING
    private static Color GetColorFromString(string colorStr)
    {
        try
        {
            return Color.FromName(colorStr);
        }
        catch
        {
            return Color.Black;
        }
    }
#endif

#if USE_SKIASHARP
    private static SKColor GetSKColorFromString(string colorStr)
    {
        try
        {
            return SKColor.Parse(colorStr);
        }
        catch
        {
            return SKColors.Black;
        }
    }
#endif
}
