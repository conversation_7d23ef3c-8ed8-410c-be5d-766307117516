{"format": 1, "restore": {"C:\\KhuongLH\\SourceCode\\BTM\\BTMHubHelper.Tests\\BTMHubHelper.Tests.csproj": {}}, "projects": {"C:\\KhuongLH\\SourceCode\\BTM\\BTMHubHelper.Tests\\BTMHubHelper.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\KhuongLH\\SourceCode\\BTM\\BTMHubHelper.Tests\\BTMHubHelper.Tests.csproj", "projectName": "BTMHubHelper.Tests", "projectPath": "C:\\KhuongLH\\SourceCode\\BTM\\BTMHubHelper.Tests\\BTMHubHelper.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\KhuongLH\\SourceCode\\BTM\\BTMHubHelper.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\KhuongLH\\SourceCode\\BTM\\BTMHubHelper\\BTMHubHelper.csproj": {"projectPath": "C:\\KhuongLH\\SourceCode\\BTM\\BTMHubHelper\\BTMHubHelper.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Azure.Functions.Worker": {"target": "Package", "version": "[2.0.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.0, )"}, "xunit": {"target": "Package", "version": "[2.5.3, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.5.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "C:\\KhuongLH\\SourceCode\\BTM\\BTMHubHelper\\BTMHubHelper.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\KhuongLH\\SourceCode\\BTM\\BTMHubHelper\\BTMHubHelper.csproj", "projectName": "BTMHubHelper", "projectPath": "C:\\KhuongLH\\SourceCode\\BTM\\BTMHubHelper\\BTMHubHelper.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\KhuongLH\\SourceCode\\BTM\\BTMHubHelper\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Humanizer.Core": {"target": "Package", "version": "[2.14.1, )"}, "Microsoft.Azure.Functions.Worker": {"target": "Package", "version": "[2.0.0, )"}, "Microsoft.Azure.Functions.Worker.Extensions.Http": {"target": "Package", "version": "[3.2.0, )"}, "Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore": {"target": "Package", "version": "[2.0.0, )"}, "Microsoft.Azure.Functions.Worker.Sdk": {"target": "Package", "version": "[2.0.0, )"}, "SkiaSharp": {"target": "Package", "version": "[3.119.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}